import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { brandOptions } from "~/modules/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "~/modules/category/hooks/category-options";
import { CategoryCode } from "~/modules/category/service/model/category";
import { measurementUnitOptions } from "~/modules/measurement-unit/hooks/measurement-unit-options";
import useUpdateProduct from "~/modules/product/hooks/use-update-product";
import type { Product } from "~/modules/product/service/model/product";
import { UpdateSupplierSchema } from "./schema";

interface UseEditSupplierFormProps {
	product: Product;
}

export default function useEditSupplierForm({
	product,
}: UseEditSupplierFormProps) {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useUpdateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	// Find the suppliers category
	const supplierCategory = categories.find(
		(cat) => cat.code === CategoryCode.SUPPLIERS,
	);

	// Fetch subcategories for suppliers
	const { data: supplierSubcategories = [] } = useQuery({
		...categorySubcategoriesOptions(service, supplierCategory?.id || ""),
		enabled: !!supplierCategory?.id,
	});

	const form = useAppForm({
		defaultValues: {
			name: product.name,
			commercialName: product.commercialName,
			code: product.code,
			skuCode: product.skuCode,
			brandID: product.brandID,
			measurementUnitID: product.measurementUnitID,
			categoryIDs: product.categoryIDs,
			state: product.state,
			description: product.description || "",
			canBeSold: product.canBeSold,
			canBePurchased: product.canBePurchased,
			costPrice: product.costPrice,
		} as UpdateSupplierSchema,
		validators: {
			onChange: UpdateSupplierSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: product.id,
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
				},
				{
					onSuccess: () => {
						toast.success("Insumo actualizado exitosamente");
						navigate({ to: "/admin/products/suppliers" });
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return {
		form,
		isPending,
		brands,
		measurementUnits,
		supplierSubcategories,
	};
}
